import { getServerSession } from "next-auth/next";
import { NextResponse } from "next/server";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

export async function GET() {
  const session = await getServerSession(authOptions);
  if (!session?.user?.roles?.includes("system_admin")) {
    return NextResponse.json({ error: "Forbidden" }, { status: 403 });
  }

  try {
    const result = await sql`
      SELECT 
        position_id,
        position_title as title,
        position_description as description
      FROM uw.position
      ORDER BY position_title
    `;

    return NextResponse.json(result);
  } catch (err) {
    console.error('Error fetching positions:', err);
    return NextResponse.json(
      { error: err instanceof Error ? err.message : "Failed to fetch position data" },
      { status: 500 }
    );
  }
} 