// app/api/sabbatical/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/lib/auth';
import { sql } from "@/app/lib/db";
import { cookies } from 'next/headers'; // to store requestId in cookie

await sql `SET search_path TO uw`;


// Zod schema validation
const SabbaticalSchema = z.object({

  lab_room_number: z.string().optional(),
  applicant_name: z.string().optional(),
  dept_name: z.string().optional(),
  abscensce_date: z.string().optional(),
  delg_supervisor: z.string().optional(),
  dept_head: z.string().optional(),
  lab_responsibility: z.string().optional(),
  emergency_name: z.string().optional(),
  lab_manager_name: z.string().optional(),

});

// POST handler
export async function POST(request: Request) {

    // Get Cookies Value for Appl ID
    const cookieStore = await cookies();
    const formData = cookieStore.get('formData')?.value;
    if (!formData) {
      throw new Error("formData cookie is missing.");
    }
    const parsed = JSON.parse(formData);
    const requestId = parsed.requestId;
    // Get Cookies Value for Appl ID End

  try {
            const session = await getServerSession(authOptions);
            if (!session?.user?.id) {
              return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
            }

            const body = await request.json();
            // Validate incoming request
            const result = SabbaticalSchema.safeParse(body);

            if (!result.success) {
              return NextResponse.json({ success: false, error: "Validation failed", details: result.error.flatten() }, { status: 400 });
            }
            const data = result.data;
            const { lab_room_number, applicant_name, dept_name, abscensce_date, delg_supervisor, dept_head, lab_responsibility, emergency_name, lab_manager_name} = data;
            let insertedId: string | undefined;

            try {

              await sql.begin(async (sql) => {

                  const res = await sql `
                    INSERT INTO uw.subbatical_laboratory_supervision (
                      appl_id, lab_room_number, applicant_name, dept_name, abscensce_date, delg_supervisor, dept_head, lab_responsibility, emergency_name, lab_manager_name
                    )
                    VALUES (
                      ${requestId ?? null}, ${lab_room_number ?? null}, ${applicant_name ?? null}, ${dept_name ?? null},
                      ${abscensce_date ?? null}, ${delg_supervisor ?? null}, ${dept_head ?? null}, ${lab_responsibility ?? null}, ${emergency_name ?? null}, ${lab_manager_name ?? null}
                    ) RETURNING id
                      `;

                    insertedId = res[0]?.id; // store inserted ID
              });

              if (!insertedId) {
                return NextResponse.json({ success: false, error: 'Failed to insert record.' }, { status: 500 });
              }
    
              return NextResponse.json({ success: true, requestId: insertedId });
    
            } catch (err) {
              console.error("DB Error:", err);
              return NextResponse.json({ success: false, error: "Database error" }, { status: 500 });
            }


      } catch (error) {
              console.error('Error fetching roles:', error);
              return NextResponse.json(
                { error: 'Failed to fetch roles' },
                { status: 500 }
                );
      }
}