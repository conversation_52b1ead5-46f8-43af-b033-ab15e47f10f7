import { NextRequest, NextResponse } from 'next/server';
import { sql } from '@/app/lib/db';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/lib/auth';

export async function POST(request: NextRequest) {

  try {

    const session = await getServerSession(authOptions);

    if (session?.user?.roles?.includes("public_user") && session?.user?.roles?.includes("regular_user")) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { userId, approveRequest, approvSignature } = await request.json();

    if (!userId || !approvSignature) {
      return NextResponse.json(
        { error: 'Signature are required' },
        { status: 400 }
      );
    }

    if (session?.user?.roles?.includes("department_support") || session?.user?.roles?.includes("department_admin") || session?.user?.roles?.includes("department_approver")) {
    // Add system role
    await sql`
      UPDATE uw.subbatical_approvals SET 
      unit_head_approve = ${approveRequest},
      unit_head_date = ${new Date().toISOString().split('T')[0]},
      unit_head_sign = ${approvSignature},
      updated_at = NOW()
      WHERE uw.subbatical_approvals.appl_id = ${userId}
    `;

    }else if (session?.user?.roles?.includes("faculty_support") || session?.user?.roles?.includes("faculty_admin") || session?.user?.roles?.includes("faculty_approver")) {
    // Add system role
    await sql`
      UPDATE uw.subbatical_approvals SET 
      dean_approve = ${approveRequest},
      dean_approve_date = ${new Date().toISOString().split('T')[0]},
      dean_sign = ${approvSignature},
      updated_at = NOW()
      WHERE uw.subbatical_approvals.appl_id = ${userId}
    `;

    }else if (session?.user?.roles?.includes("division_support") || session?.user?.roles?.includes("division_admin") || session?.user?.roles?.includes("division_approver")) {
    // Add system role
    await sql`
      UPDATE uw.subbatical_approvals  SET
      prov_office_approve = ${approveRequest}, 
      prov_office_approve_date = ${new Date().toISOString().split('T')[0]},
      prov_off_sign = ${approvSignature},
      updated_at = NOW()
      WHERE uw.subbatical_approvals.appl_id = ${userId}
    `;

    } else {
      return new Response("Unauthorized user type", { status: 403 });
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Error adding system role:', error);
    return NextResponse.json(
      { error: 'Failed to add system role' },
      { status: 500 }
    );
  }
}