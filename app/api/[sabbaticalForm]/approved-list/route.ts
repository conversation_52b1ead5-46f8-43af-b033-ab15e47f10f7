import { getServerSession } from "next-auth/next";
import { NextResponse } from "next/server";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";
import { forbidden, handleApiError } from "@/lib/error-handler";
import { redirect } from "next/navigation";
import bcrypt from "bcryptjs";

export async function GET() {

  const session = await getServerSession(authOptions);
  
  console.log(session);

  await sql `SET search_path TO uw`;

  try {

    const session = await getServerSession(authOptions);

    if (session?.user?.roles?.includes("public_user") || session?.user?.roles?.includes("regular_user")) {
      let facultySsoId =session?.user?.facultySsoId;
      console.log("here for user id");
        const users = await sql`
        SELECT
          sbrq.appl_id as user_id,
          sbrq.first_name,
          fc.work_email,
          sbrq.last_name,
          sbrq.current_leaves as leave_type,
          sbrq.created_at,
          sbrq.facultyssoid ,
          sbrq.first_name || ' ' || sbrq.last_name AS display_name,
          sbap.unit_head_approve,  sbap.dean_approve, sbap.prov_office_approve,
          sr.appl_id as sr_appl_id
        FROM uw.sabbatical_requests sbrq
        LEFT JOIN uw.subbatical_approvals sbap 
          ON sbrq.appl_id = sbap.appl_id AND sbrq.deleted_at IS NULL
        LEFT JOIN uw.faculty fc 
          ON LOWER(fc.sso_id) = LOWER(sbrq.facultyssoid)
        LEFT JOIN uw.sabbatical_reports sr ON sr.appl_id = sbrq.appl_id
        where (sbap.unit_head_approve  IS NOT NULL and  sbap.dean_approve  IS NOT NULL and sbap.prov_office_approve IS Not NULL)
        and sbrq.facultyssoid = ${facultySsoId ?? null}
        ORDER BY sbrq.created_at DESC;
      `;
              /*LEFT JOIN uw.sabbatical_reports sr 
          ON sr.appl_id = sbrq.appl_id
        WHERE sr.appl_id IS NULL*/
     // return NextResponse.json(users);
      return NextResponse.json({
        role: session,
        users: users ?? [],
      });


    }else{
      
      console.log("here for admin");
        // Fetch all users with their roles
        const users = await sql`
        SELECT
          sbrq.appl_id as user_id,
          sbrq.first_name,
          fc.work_email,
          sbrq.last_name,
          sbrq.current_leaves as leave_type,
          sbrq.created_at,
          sbrq.facultyssoid ,
          sbrq.first_name || ' ' || sbrq.last_name AS display_name,
          sbap.unit_head_approve,  
          sbap.dean_approve, 
          sbap.prov_office_approve,
          sr.appl_id as sr_appl_id
        FROM uw.sabbatical_requests sbrq
        LEFT JOIN uw.subbatical_approvals sbap 
          ON sbrq.appl_id = sbap.appl_id AND sbrq.deleted_at IS NULL
        LEFT JOIN uw.faculty fc 
          ON LOWER(fc.sso_id) = LOWER(sbrq.facultyssoid)
        LEFT JOIN uw.sabbatical_reports sr ON sr.appl_id = sbrq.appl_id
        where (sbap.unit_head_approve  IS NOT NULL and  sbap.dean_approve  IS NOT NULL and sbap.prov_office_approve IS Not NULL)
        ORDER BY sbrq.created_at DESC;
      `;
      
      //return NextResponse.json(users);
       return NextResponse.json({
        role: session,
        users: users ?? [],
      });

    }

  } catch (error) {

    const { error: errorMessage, status } = handleApiError(error);
    return NextResponse.json({ error: errorMessage }, { status });

  }

}

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    // Check authorization
    if (!session?.user?.roles?.includes("system_admin") && !session?.user?.roles?.includes("faculty_admin")) {
      throw forbidden("You don't have permission to create users");
    }

    const { email, display_name, password } = await request.json();

    if (!email || !display_name || !password) {
      return NextResponse.json(
        { error: "Email, display name, and password are required" },
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUser = await sql`
      SELECT user_id FROM common.user WHERE email = ${email}
    `;

    if (existingUser.length > 0) {
      return NextResponse.json(
        { error: "User with this email already exists" },
        { status: 400 }
      );
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Use a transaction
    const result = await sql.begin(async (sql) => {
      // Insert into common.user
      const userResult = await sql`
        INSERT INTO common.user (email, display_name)
        VALUES (${email}, ${display_name})
        RETURNING user_id
      `;

      const userId = userResult[0].user_id;

      // Insert into common.password
      await sql`
        INSERT INTO common.password (user_id, password_hash)
        VALUES (${userId}, ${hashedPassword})
      `;

      // Check if user exists in faculty table and has 'Regular Faculty' job family
      const facultyCheck = await sql`
        SELECT f.faculty_id, f.primary_unit_id
        FROM uw.faculty f
        WHERE f.work_email = ${email}
          AND f.job_family = 'Regular Faculty'
          AND f.is_deleted = FALSE
      `;

      if (facultyCheck.length > 0) {
        const faculty = facultyCheck[0];

        // Get regular_user role ID from uw.institution_role
        const [regularRole] = await sql`
          SELECT role_id FROM uw.institution_role
          WHERE role_name = 'regular_user' AND is_deleted = FALSE
        `;

        if (regularRole) {
          // Add regular_user role to faculty_institution_role
          await sql`
            INSERT INTO uw.faculty_institution_role (
              faculty_id,
              institution_role_id,
              unit_id
            )
            VALUES (
              ${faculty.faculty_id},
              ${regularRole.role_id},
              ${faculty.primary_unit_id}
            )
          `;
        }
      }

      return { userId };
    });

    return NextResponse.json({
      message: "User created successfully",
      userId: result.userId
    });
  } catch (error) {
    const { error: errorMessage, status } = handleApiError(error);
    return NextResponse.json({ error: errorMessage }, { status });
  }
}