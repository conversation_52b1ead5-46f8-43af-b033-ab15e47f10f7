import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import PositionTable from "./components/PositionTable";
import PositionHierarchy from "./components/PositionHierarchy";

export default function PositionPage() {
  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">Position Data</h1>
      <Tabs defaultValue="table" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="table">Table View</TabsTrigger>
          <TabsTrigger value="hierarchy">Hierarchy View</TabsTrigger>
        </TabsList>
        <TabsContent value="table">
          <PositionTable />
        </TabsContent>
        <TabsContent value="hierarchy">
          <PositionHierarchy />
        </TabsContent>
      </Tabs>
    </div>
  );
} 