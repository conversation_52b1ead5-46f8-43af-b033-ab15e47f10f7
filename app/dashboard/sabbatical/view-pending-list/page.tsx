"use client";
import { Suspense } from 'react';
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import SuccessMessage from '@/app/ui/sabbatical/success-message';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/app/components/ui/skeleton";
import SearchBar from "@/app/dashboard/sys_admin/faculty/components/SearchBar";
import UserRequestManager from "./components/UserRequestManager";
import { SimpleDialog } from "./components/SimpleDialog";

interface LeaveType {
  type: string;
  from: string;
  to: string;
  salaryArrangement: string;
}

interface SabbRequest {
  user_id: string;
  work_email: string;
  display_name: string;
  created_at: string;
  leave_type?: LeaveType[];
  from_Date: string;
  to_Date: string;
  first_name:string;
  last_name:string;
  unit_head_approve:string;
  dean_approve:string;
  prov_office_approve:string;
  approveRequest: string;
  approvSignature: string;
}

interface SabbRole {
  email: string;
  facultyId: string;
  facultySsoId: string;
  id: string;
  roles: string[];
}

export default function PendingListPage() {
  const router = useRouter();
  const [role, setRole] = useState<SabbRole[]>([]);
  const [users, setUsers] = useState<SabbRequest[]>([]);
  const [filteredRequests, setFilteredRequests] = useState<SabbRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedUser, setSelectedUser] = useState<SabbRequest | null>(null);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/sabatical/pending-list");

      if (!response.ok) {
        throw new Error("Failed to fetch Pending List");
      }
      const data = await response.json();

      setRole(data.role.user);
      setUsers(data.users);

      setFilteredRequests(data.users);
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);


  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredRequests(users);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = users.filter(user =>
      (user.display_name?.toLowerCase() || '').includes(query) ||
      (user.work_email?.toLowerCase() || '').includes(query)
    );
    setFilteredRequests(filtered);
  }, [searchQuery, users]);

  if (loading) {
    return (
      <div className="container mx-auto py-6 space-y-4">
        <Skeleton className="h-8 w-[200px]" />
        <Skeleton className="h-4 w-[300px]" />
        <Skeleton className="h-4 w-[250px]" />
      </div>
    );
  }

  if (error) {
    return <div className="text-red-500">Error: {error}</div>;
  }

  return (<div className="container mx-auto py-6">
    <div>
       <Suspense fallback={<div>Loading...</div>}>
                    <SuccessMessage />
        </Suspense>

    </div>
    
               
      <Card>

        <CardContent>
          <div className="mb-4 max-w-md float-left -ml-3"><CardHeader>
          <CardTitle>Pending Sabbatical Leave Requests</CardTitle>
          </CardHeader></div>
          <div className="mt-4 max-w-md float-right">
            <SearchBar
              onSearch={setSearchQuery}
              placeholder="Search by display name or email..."
            />
          </div>
          <Table>
            <TableHeader>
              <TableRow>
                
                <TableHead>Faculty Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Leave Type</TableHead>
                <TableHead>Date From</TableHead>
                <TableHead>Date To</TableHead>
                <TableHead>Approvals Status</TableHead>
                <TableHead>Created At</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredRequests.map((request) => (
                <TableRow key={request.user_id}>
                  <TableCell> {request.display_name}</TableCell>
                  <TableCell>{request.work_email}</TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                    {request.leave_type?.map((item, index) => (
                      <span key={index} >
                        {item.type}
                      </span>
                    ))}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                    {request.leave_type?.map((item, index) => (
                      <span key={index} >
                        {item.from}
                      </span>
                    ))}

                    </div>
                    
                  </TableCell>
                  <TableCell> <div className="flex flex-wrap gap-1">
                  {request.leave_type?.map((item, index) => (
                      <span key={index} >
                        {item.to}
                      </span>
                    ))}
                    </div>
                  </TableCell>
                  <TableCell> <div className="flex flex-wrap gap-1">
                        {request.unit_head_approve?.trim() || request.unit_head_approve === 'yes' ? (
                           <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                           Chair Approved
                         </span>
                        ):(<span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">
                          Chair Pending
                        </span>)}
                     
                     {request.dean_approve?.trim() || request.unit_head_approve === 'yes' ? (
                           <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                           Dean's Approved
                         </span>
                        ):(<span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">
                          Dean's Pending
                        </span>)}

                      {request.prov_office_approve?.trim() || request.unit_head_approve === 'yes' ? (
                           <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                           Provost Approved
                         </span>
                        ):(<span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">
                          Provost Pending
                        </span>)}

                    </div>
                  </TableCell>
                 
                  <TableCell>
                    {new Date(request.created_at).toLocaleDateString()}
                  </TableCell>

                  {role[0]?.roles.includes("regular_user") || role[0]?.roles.includes("public_user") ? ('') : (
                   
                    <TableCell>
                    <div className="flex flex-wrap gap-1"> 

                      <SimpleDialog
                        isOpen={selectedUser?.user_id === request.user_id}
                        onClose={() => setSelectedUser(null)}
                        title={`Manage Request for ${request.display_name}`}
                        
                        trigger={
                          <button
                            className="px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 text-sm flex flex-wrap"
                            onClick={() => setSelectedUser(request)}
                          >
                            Manage Request
                          </button>
                        }
                      >
                        <UserRequestManager
                                                  userId={request.user_id}
                                                  userEmail={request.work_email}
                                                  approveRequest={request.approveRequest ?? ''}        // Replace with real field
                                                  approvSignature={request.approvSignature ?? ''}
                                                />
                      </SimpleDialog>

                      </div>

                    </TableCell>
                  )}
                    
                </TableRow>
              ))}
            </TableBody>
          </Table>
          <div className="mt-4 text-sm text-gray-500">
            Showing {filteredRequests.length} of {users.length} users
          </div>
        </CardContent>
      </Card>
    </div>
  );
}