'use client';

import { useState, useEffect } from 'react';
import { PlusIcon } from '@heroicons/react/24/outline';

// Simple X icon component
const XIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <line x1="18" y1="6" x2="6" y2="18"></line>
    <line x1="6" y1="6" x2="18" y2="18"></line>
  </svg>
);

interface UserRequestManagerProps {
  userId: string;
  userEmail: string;
  approveRequest: string;
  approvSignature: string;
}

export default function UserRequestManager({
  userId,
  userEmail,
}: UserRequestManagerProps) {
  const [loading, setLoading] = useState(false);
  
  const [selectedApproveRequest, setSelectedApproveRequest] = useState<string>('');
  const [approvSignature, setApprovSignature] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const handleAddApproveRequest = async () => {

    if (!selectedApproveRequest) return;
    if (!approvSignature) return;

    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      const response = await fetch('/api/sabatical/approve-request', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          userEmail,
          approveRequest: selectedApproveRequest,
          approvSignature: approvSignature,
        }),
      });

      if (response.ok) {
        setSuccess(`Request approved successfully`);
        setSelectedApproveRequest('');
      } else {
        const error = await response.json();
        setError(error.error || 'Failed to approve request');
      }
    } catch (error) {
      setError('An error occurred while submiting request approval status');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="space-y-6">
        {/* System Roles Section */}
        <div>

          <div className="flex flex-wrap gap-2 mb-4">
           <label className="ml-1 font-bold text-xs text-slate-700 /80" htmlFor="approvSignature">Enter Your Full Name as Electronic Signature</label>
           <input type="text" onChange={(e) => setApprovSignature(e.target.value)} defaultValue='' placeholder="Enter Your Full Name as Electronic Signature" id="approvSignature" name="approvSignature" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
          </div>

            <div className="flex items-end gap-2">
              <div className="flex-1">
                <label htmlFor="approve-request" className="text-sm font-medium mb-2 block">
                  Approve Request?
                </label>
                <select
                  id="approve-request"
                  value={selectedApproveRequest}
                  onChange={(e) => setSelectedApproveRequest(e.target.value)}
                  className="w-full rounded-md border border-gray-300 bg-white py-2 pl-3 pr-10 text-sm"
                  disabled={loading}
                >
                    <option value="">Approve Request</option>
                    <option value='Yes'>Yes</option>
                    <option value='No'>No</option>
                </select>
              </div>
              <button
                onClick={handleAddApproveRequest}
                disabled={!selectedApproveRequest || loading}
                className="flex items-center gap-1 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Approve
              </button>
            </div>
        </div>

      </div>

      {error && (
        <div className="text-sm text-red-500 mt-2">{error}</div>
      )}

      {success && (
        <div className="text-sm text-green-500 mt-2">{success}</div>
      )}

    </div>

  );
}
