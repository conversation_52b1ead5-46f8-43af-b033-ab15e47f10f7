import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import PositionTable from "./components/PositionTable";
import { getServerSession } from "next-auth/next";
import { redirect } from "next/navigation";
import { authOptions } from "@/app/lib/auth";

export default async function PositionControlPage() {
  const session = await getServerSession(authOptions);

  if (!session?.user) {
    redirect("/login");
  }

  const roles = session.user.roles || [];
  if (!roles.includes("system_admin") && !roles.includes("faculty_admin")) {
    redirect("/dashboard");
  }

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">Position Control</h1>
      <Tabs defaultValue="table" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="table">Table View</TabsTrigger>
        </TabsList>
        <TabsContent value="table">
          <PositionTable />
        </TabsContent>
      </Tabs>
    </div>
  );
}
