"use client";

import { useEffect, useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";

interface FilterOptions {
  jobFamilies: string[];
  level04Units: string[];
  staffingStatuses: string[];
}

interface PositionFiltersProps {
  onFilterChange: (filters: {
    jobFamily: string | null;
    level04: string | null;
    staffingStatus: string | null;
  }) => void;
}

export default function PositionFilters({ onFilterChange }: PositionFiltersProps) {
  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    jobFamilies: [],
    level04Units: [],
    staffingStatuses: [],
  });
  
  const [selectedJobFamily, setSelectedJobFamily] = useState<string | null>(null);
  const [selectedLevel04, setSelectedLevel04] = useState<string | null>(null);
  const [selectedStaffingStatus, setSelectedStaffingStatus] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch filter options
  useEffect(() => {
    const fetchFilterOptions = async () => {
      try {
        const response = await fetch('/api/position-control/filters');
        if (!response.ok) {
          throw new Error('Failed to fetch filter options');
        }
        const data = await response.json();
        setFilterOptions(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchFilterOptions();
  }, []);

  // Update filters when selections change
  useEffect(() => {
    onFilterChange({
      jobFamily: selectedJobFamily,
      level04: selectedLevel04,
      staffingStatus: selectedStaffingStatus,
    });
  }, [selectedJobFamily, selectedLevel04, selectedStaffingStatus, onFilterChange]);

  const resetFilters = () => {
    setSelectedJobFamily(null);
    setSelectedLevel04(null);
    setSelectedStaffingStatus(null);
  };

  if (loading) return <div>Loading filters...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="flex flex-col md:flex-row gap-4 mb-4">
      <div className="w-full md:w-1/4">
        <Select
          value={selectedJobFamily || ""}
          onValueChange={(value) => setSelectedJobFamily(value || null)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Job Family" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">All Job Families</SelectItem>
            {filterOptions.jobFamilies.map((jobFamily) => (
              <SelectItem key={jobFamily} value={jobFamily}>
                {jobFamily}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      
      <div className="w-full md:w-1/4">
        <Select
          value={selectedLevel04 || ""}
          onValueChange={(value) => setSelectedLevel04(value || null)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Organization Unit" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">All Units</SelectItem>
            {filterOptions.level04Units.map((unit) => (
              <SelectItem key={unit} value={unit}>
                {unit}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      
      <div className="w-full md:w-1/4">
        <Select
          value={selectedStaffingStatus || ""}
          onValueChange={(value) => setSelectedStaffingStatus(value || null)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Staffing Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">All Statuses</SelectItem>
            {filterOptions.staffingStatuses.map((status) => (
              <SelectItem key={status} value={status}>
                {status}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      
      <div className="w-full md:w-1/4">
        <Button 
          variant="outline" 
          onClick={resetFilters}
          className="w-full"
        >
          <X className="mr-2 h-4 w-4" />
          Reset Filters
        </Button>
      </div>
    </div>
  );
}
