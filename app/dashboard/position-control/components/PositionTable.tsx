"use client";

import { useEffect, useState, useCallback, useRef } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import SearchBar from "./SearchBar";
import PositionFilters from "./PositionFilters";
import { format } from "date-fns";
import { useRouter } from "next/navigation";

interface Position {
  id: number;
  position_name: string;
  reference_id: number;
  worker_type: string;
  employee_type: string;
  time_type: string;
  staffing_status: string;
  available_for_hire: string;
  worker: string;
  contract_end_date: string | null;
  business_title: string;
  job_profile: string;
  frozen: string;
  freeze_date: string | null;
  freeze_reason: string | null;
  previous_incumbent: string | null;
  position_vacate_date: string | null;
  fte: number;
  job_family: string;
  job_family_groups: string;
  cost_center: string;
  class_indicator: string;
  manager: string;
  supervisory_organization: string;
  level_03: string;
  level_04: string;
  level_05: string | null;
  level_06: string | null;
  level_07: string | null;
  import_date: string;
}

export default function PositionTable() {
  const router = useRouter();
  const fetchingRef = useRef(false);
  const [positions, setPositions] = useState<Position[]>([]);
  const [filteredPositions, setFilteredPositions] = useState<Position[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [filters, setFilters] = useState({
    jobFamily: null as string | null,
    level04: null as string | null,
    staffingStatus: null as string | null,
  });

  // Initial load without filters
  useEffect(() => {
    const initialLoad = async () => {
      if (fetchingRef.current) return;

      try {
        fetchingRef.current = true;
        setLoading(true);
        setError(null);

        const response = await fetch('/api/position-control/positions');

        if (!response.ok) {
          throw new Error('Failed to fetch position data');
        }

        const data = await response.json();
        setPositions(data);
        setFilteredPositions(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
        fetchingRef.current = false;
      }
    };

    initialLoad();
  }, []); // Only run once on mount

  // Memoized handlers
  const handleFilterChange = useCallback((newFilters: {
    jobFamily: string | null;
    level04: string | null;
    staffingStatus: string | null;
  }) => {
    setFilters(newFilters);
  }, []);

  const handleSearchChange = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  // Fetch positions with filters (only when filters or search change)
  useEffect(() => {
    // Skip if no search query and no filters are set
    if (!searchQuery && !filters.jobFamily && !filters.level04 && !filters.staffingStatus) {
      return;
    }

    const fetchPositions = async () => {
      // Prevent multiple simultaneous requests
      if (fetchingRef.current) return;

      try {
        fetchingRef.current = true;
        setLoading(true);
        setError(null);

        // Build query parameters
        const params = new URLSearchParams();
        if (searchQuery) params.append('search', searchQuery);
        if (filters.jobFamily) params.append('job_family', filters.jobFamily);
        if (filters.level04) params.append('level_04', filters.level04);
        if (filters.staffingStatus) params.append('staffing_status', filters.staffingStatus);

        const url = `/api/position-control/positions?${params.toString()}`;
        const response = await fetch(url);

        if (!response.ok) {
          throw new Error('Failed to fetch position data');
        }

        const data = await response.json();
        setPositions(data);
        setFilteredPositions(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
        fetchingRef.current = false;
      }
    };

    fetchPositions();
  }, [searchQuery, filters]);

  // Format date for display
  const formatDate = (dateString: string | null) => {
    if (!dateString) return '';
    try {
      return format(new Date(dateString), 'MMM d, yyyy');
    } catch (e) {
      return dateString;
    }
  };

  if (loading) return <div>Loading position data...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="space-y-4">
      <div className="flex flex-col space-y-4">
        <div className="max-w-md">
          <SearchBar
            onSearch={handleSearchChange}
            placeholder="Search positions..."
          />
        </div>
        <PositionFilters onFilterChange={handleFilterChange} />
      </div>

      <div className="rounded-md border overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Reference ID</TableHead>
              <TableHead>Position Name</TableHead>
              <TableHead>Business Title</TableHead>
              <TableHead>Worker</TableHead>
              <TableHead>Job Family</TableHead>
              <TableHead>Staffing Status</TableHead>
              <TableHead>Organization Unit</TableHead>
              <TableHead>FTE</TableHead>
              <TableHead>Frozen</TableHead>
              <TableHead>Available for Hire</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredPositions.length === 0 ? (
              <TableRow>
                <TableCell colSpan={10} className="text-center py-4">
                  No positions found matching the current filters
                </TableCell>
              </TableRow>
            ) : (
              filteredPositions.map((position) => (
                <TableRow
                  key={position.id}
                  className="cursor-pointer hover:bg-gray-50"
                  onClick={() => router.push(`/dashboard/position-control/${position.id}`)}
                >
                  <TableCell>{position.reference_id}</TableCell>
                  <TableCell>{position.position_name}</TableCell>
                  <TableCell>{position.business_title}</TableCell>
                  <TableCell>{position.worker || 'Vacant'}</TableCell>
                  <TableCell>{position.job_family}</TableCell>
                  <TableCell>{position.staffing_status}</TableCell>
                  <TableCell>{position.level_04}</TableCell>
                  <TableCell>{position.fte}</TableCell>
                  <TableCell>{position.frozen === 'Yes' ? 'Yes' : 'No'}</TableCell>
                  <TableCell>{position.available_for_hire}</TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
      <div className="text-sm text-gray-500">
        Showing {filteredPositions.length} positions
      </div>
    </div>
  );
}
