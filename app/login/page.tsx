import <PERSON><PERSON><PERSON> from "@/app/ui/amelia-logo";
import LoginForm from "@/app/login/login-form";
import { Suspense } from "react";

export default function LoginPage() {
  return (
    <main className="flex items-center justify-center md:h-screen">
      <div className="relative mx-auto flex w-full max-w-[400px] flex-col space-y-2.5 p-4 md:-mt-32">
        <div className="flex h-20 w-full items-end justify-center rounded-lg bg-grey-50 p-3 md:h-36">
          <div className="w-36 text-white md:w-48">
            <AmeliaLogo />
          </div>
        </div>
        <Suspense fallback={<div>Loading...</div>}>
          <LoginForm />
        </Suspense>
      </div>
    </main>
  );
}