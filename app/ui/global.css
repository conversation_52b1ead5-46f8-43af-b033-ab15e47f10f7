@tailwind base;
@tailwind components;
@tailwind utilities;


input[type='number'] {
  -moz-appearance: textfield;
  appearance: textfield;
}

input[type='number']::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.quill-editor .ql-container {
  min-height: 200px;

  border-bottom-left-radius: 0.5rem; /* optional: rounded corners */
  border-bottom-right-radius: 0.5rem; /* optional: rounded corners */
   /* Tailwind border-gray-300 */
  border: 1px solid #d1d5db;

}

.quill-editor .ql-editor {
  min-height: 180px; /* set this to ensure typing area also grows */
}

.ql-toolbar + .ql-toolbar {
  display: none !important;
}

.quill-editor .ql-toolbar {
  @apply border border-gray-300 rounded-t-lg;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --radius: 0.5rem;
  }
}



@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    /* Ensure page starts at the top */
    scroll-behavior: smooth;
    overflow-anchor: none;
  }

  /* Prevent automatic scrolling */
  html {
    scroll-behavior: smooth;
    height: 100%;
    overflow-y: auto;
  }
}
