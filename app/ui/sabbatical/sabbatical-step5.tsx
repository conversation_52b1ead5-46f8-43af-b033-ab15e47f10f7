'use client';
import { useForm, useFieldArray } from "react-hook-form";
import { useRouter } from "next/navigation";
import { useState } from "react";
import QuillEditor from '@/components/ui/quill-editor'; // Import QuillEditor
import Cookies from 'js-cookie';

type FormValues = {
  applicant_name: string;
  dept_name: string;
  administrative_appointment: string;
  department: string;
  from_date: string;
  to_date: string;
  arrangement_made: string;
  no_administrative_appointment: string;
};

export  default function Form({ initialData = {} }: { initialData?: Partial<FormValues> }) {
  const router = useRouter();
  const [error, setError] = useState("");

  const { register, control, handleSubmit } = useForm<FormValues>({
    defaultValues: {
      ...initialData,
    },
  });

  const onSubmit = async (data: FormValues) => {

          // Get Cookies Value for Appl ID
          const formData = Cookies.get('formData');
          if (!formData) throw new Error("formData cookie is missing.");
          const parsed = JSON.parse(formData);
          const grtLieuSalary = parsed.grtLieuSalary;

    try {
      const response = await fetch('/api/sabatical/sabbatical-step5', {
        method: 'POST',
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });

      const res = await response.json();
      if (res.success && grtLieuSalary=='Yes') {
        router.push('/dashboard/sabbatical/sabbatical-step6?message=Request+submitted+Complete+Next+Step&status=success');
      } else if (res.success && grtLieuSalary=='No') {
        router.push('/dashboard/sabbatical/sabbatical-show-acceptance?message=Request+submitted+Complete+Next+Step&status=success');
      } else {
        setError(res.error || 'Submission failed.');
      }
    } catch (err) {
      setError('Unexpected error occurred.');
    }
  };

  return (
 
    <div className='w-full max-w-full px-5 m-auto lg:w-11/12 '>
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 relative mb-32" >
    {error && <p className="text-red-500">{error}</p>}
    <div  className=" top-0 left-0 flex flex-col w-full min-w-0 p-4 break-words bg-white border-0   shadow-soft-xl rounded-2xl bg-clip-border h-auto opacity-100 visible">

          <div>
                <div className="flex flex-wrap mt-4 -mx-3">
                  <div className="w-full max-w-full px-3 flex-0 sm:w-4/12">
                    <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="delg_supervisor">Applicant Name</label>
                    <input type="text" {...register("applicant_name", { required: false })} defaultValue={initialData.applicant_name} placeholder="Name" id="applicant_name" name="applicant_name" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                              
                  </div>
                  <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-4/12">
                    <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Department</label>
                    <input type="text" {...register("dept_name", { required: false })} defaultValue={initialData.department} placeholder="dept_name" id="dept_name" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>`

                  </div>
                  <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-4/12">
                    <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Administrative Appointment</label>
                    <input type="text"  {...register("administrative_appointment", { required: false })} defaultValue={initialData.administrative_appointment} placeholder=""  id="administrative_appointment" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>`

                  </div>
                </div>

                
                <p className='md:mt-3 leading-normal text-sm'>During my proposed leave of absence</p>
                <div className="flex flex-wrap mt-4 -mx-3">
                  <div className="w-full max-w-full px-3 flex-0 sm:w-4/12">
                    <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="from_date">From Dates</label>
                    <input type="date" id="from_date" {...register("from_date", { required: false })}  className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                              
                  </div>
                  <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-4/12">
                    <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="to_date">To Date</label>
                    <input type="date" id="to_date" {...register("to_date", { required: false })}  className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                  </div>
                </div>

                <p className='md:mt-3 leading-normal text-transparent bg-clip-text bg-gradient-to-tl from-purple-700 to-pink-500'>The following arrangements have been made for my Administrative Appointment(s):</p>
                <div className="flex flex-wrap mt-4 -mx-3">
                  <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                    <QuillEditor
                      
                      {...register("arrangement_made")} 
                      control={control}
                      placeholder="The following arrangements have been made for my Administrative Appointment(s)"
                      defaultValue={initialData.arrangement_made || ''}
                    />

                  </div>
                
                </div>
                <p className='md:mt-3 leading-normal text-transparent bg-clip-text bg-gradient-to-tl from-purple-700 to-pink-500'>OR: Declaration of no administrative appointments during leave</p>
                

                  <div className="flex flex-wrap mt-4 -mx-3">
                    <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">

                    <input type="checkbox" id="no_administrative_appointment" {...register("no_administrative_appointment", { required: false })} value="no administrative appointment" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft  appearance-none ml-3 mr-4 border border-solid border-gray-300 bg-white bg-clip-padding font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    
                    <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="no_administrative_appointment">I do not have and will not hold an administrative appointment during my leave.</label>

                    </div>
                   
                  </div>

                  <div className="flex justify-end mt-6">
                    <button type="submit" aria-controls="address" next-form-btn=""  className="inline-block px-6 py-3 m-0 ml-2 text-xs font-bold text-center text-white uppercase align-middle transition-all border-0 rounded-lg cursor-pointer ease-soft-in leading-pro tracking-tight-soft bg-gradient-to-tl from-purple-700 to-pink-500 shadow-soft-md bg-150 bg-x-25 hover:scale-102 active:opacity-85">Save and Continue</button>
                  </div>
            </div>
      </div>

  </form>
  
  </div>


  );
}
        
      
