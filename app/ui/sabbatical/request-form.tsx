'use client';
import Link from 'next/link';
import { useForm, useField<PERSON><PERSON><PERSON>, Controller } from "react-hook-form";
import { useRouter } from "next/navigation";
import { Button } from '@/app/ui/button';

import { useState } from "react";
import QuillEditor from '@/components/ui/quill-editor'; // Import QuillEditor
//import ReactQuill from 'react-quill-new';
//import 'react-quill-new/dist/quill.snow.css';

type LeaveEntry = {
  type: string;
  from: string;
  to: string;
  salaryArrangement: string;
};

type RankInfo = {
  rank_id: string;
  rank_desc: string;
};

type LeaveType={
  leave_type:string;
}

type TenureType = {
  fac_type: string;
  fac_type_id: string;
};

type FormValues = {
  id: any,
  first_name:string;
  last_name:string;
  name: string;
  rank: string;
  department: string;
  tenure: string;
  first_rank: string;
  first_date: string;
  leaveOutline: string;
  leavetype:string;
  currentRequestTimeDate: string;
  serviceCreditRemaining: string;
  pastLeaves: LeaveEntry[];
  currentLeaves: LeaveEntry[];
  nonTeachTerm: LeaveEntry[];
  grtLieuSalary:string;

};

export default function Form({ initialData = {} }: { initialData?: Partial<FormValues> & {
  rankInfo?: RankInfo[];
  leaveType?: LeaveType[];
  tenureType?: TenureType[];
};}) {

  const router = useRouter();
  const [error, setError] = useState("");

  const { register, control, handleSubmit, getValues } = useForm<FormValues>({
    defaultValues: {
      ...initialData,
      pastLeaves: [{ type: "", from: "", to: "", salaryArrangement: "" }],
      currentLeaves: [{ type: "", from: "", to: "", salaryArrangement: "" }],
      nonTeachTerm: [{ type: "", from: "", to: "", salaryArrangement: "" }],
      leaveOutline: initialData?.leaveOutline || '',
      currentRequestTimeDate: initialData?.currentRequestTimeDate || '',
      serviceCreditRemaining: initialData?.serviceCreditRemaining || '',
      grtLieuSalary: initialData?.grtLieuSalary || '',
      rank: initialData.rank || '',
      leavetype: initialData.leavetype || '',
      first_name:initialData?.first_name || '',
      last_name:initialData?.last_name || '',
      tenure: initialData.tenure || '',
    },
  });

  const { fields: pastLeaveFields,
    append: appendPastLeave,
    remove: removePastLeave, } = useFieldArray({
    control,
    name: "pastLeaves",
  });

  const { fields: currentLeaveFields,
    append: appendCurrentLeave,
    remove: removeCurrentLeave, } = useFieldArray({
    control,
    name: "currentLeaves",
  });

  const { fields: nonTeachFields,
    append: appendNonTeachFields,
    remove: removeNonTeachFields, } = useFieldArray({
    control,
    name: "nonTeachTerm",
  });


  const onSubmit = async (data: FormValues) => {

    try {
      const response = await fetch('/api/sabbaticalForm/sabbatical-request-form', {
        method: 'POST',
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });

      const res = await response.json();
      if (res.success) {
        router.push('/dashboard/sabbatical/sabbatical-step2?message=Request+submitted+Complete+Step+2&status=success');
      } else {
        setError(res.error || 'Submission failed.');
      }
    } catch (err) {
      setError('Unexpected error occurred.');
    }

  };

  return (
    <div className='w-full max-w-full px-5 m-auto lg:w-11/12 '>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 relative mb-32" >
          {error && <p className="text-red-500">{error}</p>}
            <div  className=" top-0 left-0 flex flex-col w-full min-w-0 p-4 pl-8 pr-8 pb-8 break-words bg-white border-0 shadow-soft-xl rounded-2xl bg-clip-border h-auto opacity-100 visible">
            <div>
      </div>

                <div className="flex flex-wrap mt-4 -mx-3">
                  <div className="w-full max-w-full px-3 flex-0 sm:w-3/12">
                    <label className="mb-2 ml-1 font-bold text-xs text-slate-700" htmlFor="delg_supervisor">Name</label>
                    <input type="text" {...register("name", { required: true })} defaultValue={initialData.name} placeholder="Name" id="name" name="name" className="focus:shadow-soft-primary-outline text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>

                  </div>
                  <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-3/12">
                    <label className="mb-2 ml-1 font-bold text-xs text-slate-700" htmlFor="dept_head">Department</label>
                    <input type="text" {...register("department", { required: true })} defaultValue={initialData.department} placeholder="Department" id="department" name="department" className="focus:shadow-soft-primary-outline text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>`

                  </div>
                  <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-3/12">
                    <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Rank</label>

                    {/* Rank Dropdown */}
                    <select {...register("rank", { required: true })} defaultValue={initialData.rank || ""} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">
                      <option key="select-rank" value="" >Select Rank</option>
                      {initialData.rankInfo?.map((rank) => (
                        <option key={rank.rank_id} value={rank.rank_id}>
                          {rank.rank_desc}
                        </option>
                      ))}
                    </select>

                  </div>
                  <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-3/12">
                    <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Tenure </label>

                      <select {...register("tenure", { required: true })} defaultValue={initialData.tenure || ""} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">
                      <option key="select-tenure" value="">Select Tenure</option>
                      {initialData.tenureType?.map((tenure) => (
                        <option key={tenure.fac_type_id} value={tenure.fac_type}>
                          {tenure.fac_type}
                        </option>
                      ))}
                    </select>

                  </div>
                </div>

                <p className='md:mt-3 leading-normal text-sm font-bold'>First Appointed at University of Waterloo:</p>
                <div className="flex flex-wrap mt-4 -mx-3">
                  <div className="w-full max-w-full px-3 flex-0 sm:w-4/12">
                    <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="first_rank">Rank</label>
                    {/* Rank Dropdown */}
                    <select {...register("first_rank", { required: true })} defaultValue={initialData.rank || ""} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">
                      <option key="select-first-rank" value="">Select Rank</option>
                      {initialData.rankInfo?.map((rank) => (
                        <option key={rank.rank_id} value={rank.rank_id}>
                          {rank.rank_desc}
                        </option>
                      ))}
                    </select>

                  </div>
                  <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-4/12">
                    <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Date</label>
                    <input {...register("first_date", { required: true })} defaultValue={initialData.first_date} placeholder="Tenure"
                    id="first_date"
                    name="first_date"
                    type="date" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                  </div>
                </div>

                <p className='md:mt-3 leading-normal text-sm  font-bold'>Record of Past Leaves: <button type="button" onClick={() => appendPastLeave({ type: "", from: "", to: "", salaryArrangement: "" })} className='text-blue-700'> + Add Entry
                </button></p>

                {pastLeaveFields.map((field, index) => (
                  <div className="flex flex-wrap mt-2 -mx-3 border-b border-solid border-gray-300" key={field.id} >
                    <div className="w-full max-w-full px-3 flex-0 sm:w-3/12 ">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="first_rank">Select Leave Type</label>

                        <select {...register(`pastLeaves.${index}.type`)} defaultValue={initialData.leavetype || ""} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">
                        <option key="select-past-leave-type" value="">Select Leave Type</option>
                        {initialData.leaveType?.map((leaves, index) => (
                           <option key={index} value={leaves.leave_type}>
                           {leaves.leave_type}
                         </option>
                        ))}
                      </select>


                    </div>
                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-2/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">From</label>
                      <input
                      {...register(`pastLeaves.${index}.from`)} placeholder="From"
                      id="from"
                      type="date" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>
                    <div className="w-full max-w-full px-3  flex-0 sm:mt-0 sm:w-2/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">To</label>
                      <input
                      {...register(`pastLeaves.${index}.to`)} placeholder="To"
                      id="to"
                      type="date" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>
                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-3/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Salary Arrangement</label>
                      <input {...register(`pastLeaves.${index}.salaryArrangement`)} placeholder="Salary Arrangement"
                      className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>

                    </div>
                    <div className="p-4 rounded  sm:w-1/12">
                      <button type="button" onClick={() => removePastLeave(index)} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding mt-2 mr-2 px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">X</button>
                    </div>

                  </div>

                ))}

                <p className='md:mt-3 leading-normal text-sm font-bold'>Current Request for Leave: <button type="button" onClick={() => appendCurrentLeave({ type: "", from: "", to: "", salaryArrangement: "" })} className='text-blue-700'> + Add Entry
                </button></p>

                {currentLeaveFields.map((field, index) => (
                  <div className="flex flex-wrap mt-2 -mx-3 border-b border-solid border-gray-300" key={field.id} >
                    <div className="w-full max-w-full px-3 flex-0 sm:w-3/12 ">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" >Select Leave Type</label>

                      <select {...register(`currentLeaves.${index}.type`)} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">
                        <option key="select-current-leave-type" value="">Select Leave Type</option>
                        {initialData.leaveType?.map((leaves, index) => (
                           <option key={index} value={leaves.leave_type}>
                           {leaves.leave_type}
                         </option>
                        ))}
                      </select>


                    </div>
                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-2/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">From</label>
                        <input
                        {...register(`currentLeaves.${index}.from`)}
                      id="from"
                      type="date" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>
                    <div className="w-full max-w-full px-3  flex-0 sm:mt-0 sm:w-2/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">To</label>
                      <input
                      {...register(`currentLeaves.${index}.to`)}
                      id="to"
                      type="date" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>
                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-3/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Salary Arrangement</label>
                      <input {...register(`currentLeaves.${index}.salaryArrangement`)} placeholder="Salary Arrangement"
                     className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>

                    </div>
                    <div className="p-4 rounded  sm:w-1/12">
                      <button type="button" onClick={() => removeCurrentLeave(index)} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding mt-2 mr-2 px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">X</button>
                    </div>

                  </div>

                ))}

                <p className='md:mt-3 leading-normal text-sm font-bold'>Non-teaching term(s): if leave is to be combined with non-teaching terms, specify non-teaching term dates below. <button type="button" onClick={() => appendNonTeachFields({ type: "", from: "", to: "", salaryArrangement: "" })} className='text-blue-700'> + Add Entry
                                </button></p>

                {nonTeachFields.map((field, index) => (
                  <div className="flex flex-wrap mt-2 -mx-3 border-b border-solid border-gray-300" key={field.id}>
                    <div className="w-full max-w-full px-3 flex-0 sm:w-3/12 " >
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="first_rank">Select Leave Type</label>

                      <select {...register(`nonTeachTerm.${index}.type`)} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">
                        <option key="select-non-teach-leave-type" value="">Select Leave Type</option>
                        {initialData.leaveType?.map((leaves, index) => (
                           <option key={index} value={leaves.leave_type}>
                           {leaves.leave_type}
                         </option>
                        ))}
                      </select>


                    </div>
                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-2/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">From</label>
                      <input
                      {...register(`nonTeachTerm.${index}.from`)}
                      id="from"
                      type="date" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>
                    <div className="w-full max-w-full px-3  flex-0 sm:mt-0 sm:w-2/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">To</label>
                      <input
                      {...register(`nonTeachTerm.${index}.to`)}
                      id="to"
                      type="date" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>
                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-3/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Salary Arrangement</label>
                      <input {...register(`nonTeachTerm.${index}.salaryArrangement`)} placeholder="Salary Arrangement"
                      className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>
                    <div className="p-4 rounded  sm:w-1/12">
                      <button type="button" onClick={() => removeNonTeachFields(index)} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding mt-2 mr-2 px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">X</button>
                    </div>
                  </div>

                ))}

                <p className='md:mt-3 leading-normal text-sm'>Outline of Leave: 60 words max. stating your area of research, plans for leave, and expected outcomes (for Board of Governors report)</p>
                <div className="flex flex-wrap mt-4 -mx-3">
                  <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">

                  <QuillEditor
                    name="leaveOutline"
                    control={control}
                    placeholder="Outline of Leave: 60 words max. stating your area of research, plans for leave, and expected outcomes (for Board of Governors report)"
                    defaultValue={initialData.leaveOutline || ''}
                  />
                  </div>

                </div>

                <p className='md:mt-3 leading-normal text-sm'>If the current request is partially based on credit for time spent in an administrative position, please specify post and dates</p>
                <div className="flex flex-wrap mt-4 -mx-3">
                  <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">

                  <QuillEditor
                    name="currentRequestTimeDate"
                    control={control}
                    placeholder="If the current request is partially based on credit for time spent in an administrative position, please specify post and dates"
                    defaultValue={initialData.currentRequestTimeDate || ''}
                  />

                  </div>
                </div>

                <p className='md:mt-3 leading-normal text-sm'>Service Credit/Administrative Credit remaining</p>
                <div className="flex flex-wrap mt-4 -mx-3">
                  <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">

                  <QuillEditor
                    name="serviceCreditRemaining"
                    control={control}
                    placeholder="Service Credit/Administrative Credit remaining"
                    defaultValue={initialData.serviceCreditRemaining || ''}
                  />

                  </div>
                </div>

                <p className='md:mt-3 leading-normal text-sm'>Service Credit/Administrative Credit remaining</p>

                    <ul className='float-left pl-6 mb-0 list-disc text-slate-500 w-full'>
                    <li className='leading-normal text-sm'><strong>Please complete Step 2:</strong> declaration of any outside employment or fellowship; declaration of any external grant funding; travel declaration</li>
                    <li className='leading-normal text-sm'><strong>Please complete Step 3:</strong> Graduate Student Supervision During Leave of Absence form (attached)</li>
                    <li className='leading-normal text-sm'><strong>Please complete Step 4:</strong> Laboratory Safety Supervision During Leave of Absence form (attached)  </li>
                    <li className='leading-normal text-sm'><strong>Please complete Step 5:</strong> Administrative Appointment During Leave of Absence form (attached)</li>
                    <li className='leading-normal text-sm'><strong>Are you requesting a grant in lieu of salary?</strong>
                        <div className="flex flex-wrap mt-4 -mx-3 mb-4">
                          <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                          <input type='radio' id="yes" {...register("grtLieuSalary", { required: true })} value='Yes' className='mr-3'/> <label htmlFor='yes' className='mr-4'>Yes</label>
                          <input type='radio' id="no" {...register("grtLieuSalary")} value='No'  className='mr-3' /> <label htmlFor='no' className='mr-4'>No</label>
                          </div>
                        </div>

                     </li>
                    <li className='leading-normal text-sm'>Please complete if applicable Step 6:  application for Research Grant.</li>
                    </ul>

                    <div className="flex justify-end mt-6">
          <button type="submit" aria-controls="address" next-form-btn=""  className="inline-block px-6 py-3 m-0 ml-2 text-xs font-bold text-center text-white uppercase align-middle transition-all border-0 rounded-lg cursor-pointer ease-soft-in leading-pro tracking-tight-soft bg-gradient-to-tl from-purple-700 to-pink-500 shadow-soft-md bg-150 bg-x-25 hover:scale-102 active:opacity-85">Save and Continue</button>
        </div>


            </div>

          </form>

    </div>
  );
}


