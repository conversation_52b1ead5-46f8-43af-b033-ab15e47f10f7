'use client';
import { useForm, useFieldArray } from "react-hook-form";
import { useRouter } from "next/navigation";
import { useState } from "react";
import QuillEditor from '@/components/ui/quill-editor'; // Import QuillEditor

type PersonnelEntry = {
  name_position: string;
  rate_per_annum: string;
  hours_per_week: string;
  calc_rate: string;
  fringe_benefit: string;
  est_expenses: string;
  cost: string;
};

type TravelEntry = {
  location: string;
  duration: string;
  travel_mode: string;
  related_cost: string;
  cost: string;
};

type OtherEntry = {
  quantity: string;
  description: string;
  unit_cost: string;
  cost: string;
};

type FormValues = {
  appl_id: string;
  applicant_name: string;
  department: string;
  date: string;
  no_of_month: string;
  starting_term: string;
  ending_term: string;
  project_title: string;
  research_location: string;
  amount_requested: string;
  research_description: string;
  personnel: PersonnelEntry[];
  travel: TravelEntry[];
  equipment: OtherEntry[];
  supplies: OtherEntry[];
  otherexpenses: OtherEntry[];
  totalfundrequested:string;
};

export  default function Form({ initialData = {} }: { initialData?: Partial<FormValues> }) {
  const router = useRouter();
  const [error, setError] = useState("");

  const { register, control, handleSubmit } = useForm<FormValues>({
    defaultValues: {
      ...initialData,

      personnel: [{ name_position: "",  rate_per_annum: "",  hours_per_week: "",  calc_rate: "",  fringe_benefit: "",  est_expenses: "",  cost: "" }],
      travel: [{ location: "", duration: "", travel_mode: "", related_cost: "", cost: "" }],
      equipment: [{ quantity: "",  description: "",  unit_cost: "",  cost: "" }],
      supplies: [{ quantity: "",  description: "",  unit_cost: "",  cost: "" }],
      otherexpenses: [{ quantity: "",  description: "",  unit_cost: "",  cost: "" }],

    },
  });

  const { fields: personnelFields,
    append: appendPersonnel,
    remove: removePersonnel, } = useFieldArray({
    control,
    name: "personnel",
  });

  const { fields: travelFields,
    append: appendTravel,
    remove: removeTravel, } = useFieldArray({
    control,
    name: "travel",
  });

  const { fields: equipmentFields,
    append: appendEquipment,
    remove: removeEquipment, } = useFieldArray({
    control,
    name: "equipment",
  });

  const { fields: suppliesFields,
    append: appendSupplies,
    remove: removeSupplies, } = useFieldArray({
    control,
    name: "supplies",
  });
  
  const { fields: otherexpensesFields,
    append: appendOtherExpenses,
    remove: removeOtherExpenses, } = useFieldArray({
    control,
    name: "otherexpenses",
  });

  


  const onSubmit = async (data: FormValues) => {
    try {
      const response = await fetch('/api/sabatical/sabbatical-step6', {
        method: 'POST',
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });

      const res = await response.json();
      if (res.success) {
        router.push('/dashboard/sabbatical/sabbatical-show-acceptance?message=Request+submitted&status=success');
      } else {
        setError(res.error || 'Submission failed.');
      }
    } catch (err) {
      setError('Unexpected error occurred.');
    }
  };

  return (
 
    <div className='w-full max-w-full px-5 m-auto lg:w-11/12 '>
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 relative mb-32" >
    {error && <p className="text-red-500">{error}</p>}
    <div  className=" top-0 left-0 flex flex-col w-full min-w-0 p-4 break-words bg-white border-0   shadow-soft-xl rounded-2xl bg-clip-border h-auto opacity-100 visible">

          <div>
                <div className="flex flex-wrap mt-4 -mx-3">
                  <div className="w-full max-w-full px-3 flex-0 sm:w-4/12">
                    <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="delg_supervisor">Applicant Name</label>
                    <input type="text" {...register("applicant_name", { required: false })} defaultValue={initialData.applicant_name} placeholder="Name" id="applicant_name" name="applicant_name" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                              
                  </div>
                  <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-4/12">
                    <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Department</label>
                    <input type="text" {...register("department", { required: false })} defaultValue={initialData.department} placeholder="dept_name" id="dept_name" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>`

                  </div>
                  <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-4/12">
                    <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Date</label>
                    <input type="date" id="date" {...register("date", { required: true })}  value={new Date().toISOString().split('T')[0]} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none" readOnly/>

                  </div>
                </div>
                <h2 className='md:mt-3 leading-normal text-transparent bg-clip-text bg-gradient-to-tl from-purple-700 to-pink-500 '>A. BASIC INFORMATION</h2>

                <p className='md:mt-3 leading-normal text-sm mb-2 ml-1 font-bold text-xs text-slate-700 /80'>1.	Indicate the Term of Research Leave Granted:: </p>

                  <div className="flex flex-wrap mt-2 -mx-3 " >
                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-4/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Starting</label>
                      <input 
                      {...register(`starting_term`)} placeholder="Starting Term"
                      id="starting_term"
                      
                      type="text" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>
                    <div className="w-full max-w-full px-3  flex-0 sm:mt-0 sm:w-4/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Ending</label>
                      <input 
                      {...register(`ending_term`)} placeholder="Ending Term"
                      id="ending_term"
                      
                      type="text" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>
                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-4/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Number of Months</label>
                      <input {...register(`no_of_month`)} placeholder="Number of Months" type="text"
                      id="no_of_month" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                       
                    </div>
                  </div>

                  <p className='md:mt-3 leading-normal text-sm mb-2 ml-1 font-bold text-xs text-slate-700 /80'>2.	Title of Research Project:</p>
                
                  <div className="flex flex-wrap mt-4 -mx-3">
                      <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                        <input type="text" {...register("project_title", { required: false })} defaultValue={initialData.project_title} placeholder="" id="project_title" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>`
                      </div>
                  </div>

                  <p className='md:mt-3 leading-normal text-sm mb-2 ml-1 font-bold text-xs text-slate-700 /80'>3.	Location of Research (Please name principal building or site where the research will be performed, for example, a college, hospital, main campus, or site in the Arctic):</p>
                
                  <div className="flex flex-wrap mt-4 -mx-3">
                      <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                        <input type="text" {...register("research_location", { required: false })} defaultValue={initialData.research_location} placeholder="" id="research_location" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>`
                      </div>
                  </div>


                  <p className='md:mt-3 leading-normal text-sm mb-2 ml-1 font-bold text-xs text-slate-700 /80'>4.	Total Amount Requested:</p>
                
                  <div className="flex flex-wrap mt-4 -mx-3">
                      <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                        <input type="text" {...register("amount_requested", { required: false })} defaultValue={initialData.amount_requested} placeholder="" id="amount_requested" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>`
                      </div>
                      <div className="w-full max-w-full px-3 flex-0 sm:w-12/12 text-xs text-slate-500">
                      Please note that T4A slips will be issued for the taxation year in which the installment payment is received, not the date on which the Payroll department receives the Research Grant Payment Form. (Installments will be  included  in  the  normal  salary  payments).  Visit  Revenue  Canada <a href="http://www.cra-arc.gc.ca/tx/tchncl/ncmtx/fls/s1/f2/s1-f2-c3-eng.html#N108BF">http://www.cra-arc.gc.ca/tx/tchncl/ncmtx/fls/s1/f2/s1-f2-c3-eng.html#N108BF</a> for further information.
                      </div>
                  </div>


                <p className='md:mt-3 leading-normal text-transparent bg-clip-text bg-gradient-to-tl from-purple-700 to-pink-500'>B.	DESCRIPTION OF PROPOSED RESEARCH</p>
                <div className="flex flex-wrap mt-4 -mx-3">
                  <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                    <label className="font-bold text-xs text-slate-700 /80" htmlFor="research_description">Please give a description of the research objectives and procedures and a justification of the budget items listed under Section C and the choice of location(s), if any. Please note that the purpose and objects of the expenditures proposed must be warranted in the context of the research outlined. Applications that do not provide sufficient information will be returned.</label>
                    <QuillEditor
                      
                      {...register("research_description")} 
                      control={control}
                      placeholder="The following arrangements have been made for my Administrative Appointment(s)"
                      defaultValue={initialData.research_description || ''}
                    />

                  </div>
                
                </div>


                <p className='md:mt-3 leading-normal text-transparent bg-clip-text bg-gradient-to-tl from-purple-700 to-pink-500'>C.	BUDGET</p>
                

                  <div className="flex flex-wrap mt-4 -mx-3">
                    <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                          <label className="font-bold text-xs text-slate-700 /80" htmlFor="research_description">Please give a description of the research objectives and procedures and a justification of the budget items listed under Section C and the choice of location(s), if any. Please note that the purpose and objects of the expenditures proposed must be warranted in the context of the research outlined. Applications that do not provide sufficient information will be returned.</label>
                        <input type="text" {...register("amount_requested", { required: false })} defaultValue={initialData.amount_requested} placeholder="" id="amount_requested" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>`

                    </div>
                   
                  </div>

                  <p className='md:mt-3 leading-normal text-sm font-semibold'>PERSONNEL: <button type="button" onClick={() => appendPersonnel({ name_position: "",  rate_per_annum: "",  hours_per_week: "",  calc_rate: "",  fringe_benefit: "",  est_expenses: "",  cost: "" })} className="float-right"> + Add Entry
                </button></p>
                 
                {personnelFields.map((field, index) => (
                  <div className="flex flex-wrap mt-2 -mx-3 border-b border-solid border-gray-300  mb-5" key={field.id} >
                    <div className="w-full max-w-full px-3 flex-0 sm:w-3/12 ">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="first_rank">Name, position, and qualifications</label>
                      <input 
                      {...register(`personnel.${index}.name_position`)} placeholder="Name Position"
                      id="name_position"
                      type="text" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>

                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-1/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Rate/Annum</label>
                      <input 
                      {...register(`personnel.${index}.rate_per_annum`)} placeholder=""
                      id="rate_per_annum"
                      type="text" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>


                    <div className="w-full max-w-full px-3  flex-0 sm:mt-0 sm:w-1/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Hours/Week</label>
                      <input 
                      {...register(`personnel.${index}.hours_per_week`)} placeholder=""
                      id="hours_per_week"
                      type="text" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>

                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-1/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Calc. Rate</label>
                      <input {...register(`personnel.${index}.calc_rate`)} placeholder=""
                      id="calc_rate"
                      className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                       
                    </div>

                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-2/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Fringe Benefits</label>
                      <input {...register(`personnel.${index}.fringe_benefit`)} 
                      id="fringe_benefit"
                      className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                       
                    </div>
                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-2/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Estimated Expenses</label>
                      <input {...register(`personnel.${index}.est_expenses`)} placeholder=""
                      id="est_expenses"
                      className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                       
                    </div>
                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-1/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="cost">$</label>
                      <input {...register(`personnel.${index}.cost`)} placeholder=""
                      id="cost"
                       className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                       
                    </div>

                    <div className="p-4 rounded  sm:w-1/12">
                      <button type="button" onClick={() => removePersonnel(index)} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding mt-2 mr-2 px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">X</button>
                    </div>
                  
                  </div>

                ))}


                <p className='md:mt-3 leading-normal text-sm '><b>TRAVEL AND RELATED COSTS FOR PRINCIPAL INVESTIGATOR</b> essential to research program (expense for sojourning and for spouse and family are not allowable) &nbsp;&nbsp;<button type="button" onClick={() => appendTravel({ location: "", duration: "", travel_mode: "", related_cost: "", cost: "" })} className="float-right"> + Add Entry
                </button></p>

                {travelFields.map((field, index) => (
                  <div className="flex flex-wrap mt-2 -mx-3 border-b border-solid border-gray-300  mb-5" key={field.id} >
                    <div className="w-full max-w-full px-3 flex-0 sm:w-3/12 ">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="first_rank">Location(s)</label>
                      <input 
                      {...register(`travel.${index}.location`)} placeholder=""
                      id="location"
                      type="text" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>

                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-3/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="duration">Duration</label>
                      <input 
                      {...register(`travel.${index}.duration`)} placeholder=""
                      id="duration"
                      type="text" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>


                    <div className="w-full max-w-full px-3  flex-0 sm:mt-0 sm:w-2/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="travel_mode">Travel Mode</label>
                      <input 
                      {...register(`travel.${index}.travel_mode`)} placeholder=""
                      id="travel_mode"
                      type="text" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>

                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-2/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="related_cost">Related Costs</label>
                      <input {...register(`travel.${index}.related_cost`)} placeholder=""
                      id="related_cost"
                      className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                       
                    </div>


                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-1/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="cost">$</label>
                      <input {...register(`travel.${index}.cost`)} placeholder=""
                      id="cost"
                       className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>

                    <div className="p-4 rounded  sm:w-1/12">
                      <button type="button" onClick={() => removeTravel(index)} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding mt-2 mr-2 px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">X</button>
                    </div>
                  
                  </div>

                ))}


                <p className='md:mt-3 leading-normal text-sm '><b>EQUIPMENT </b> (list specific items) &nbsp;&nbsp;<button type="button" onClick={() => appendEquipment({ quantity: "", description: "", unit_cost: "", cost: "" })} className="float-right"> + Add Entry
                </button></p>

                {equipmentFields.map((field, index) => (
                  <div className="flex flex-wrap mt-2 -mx-3 border-b border-solid border-gray-300  mb-5" key={field.id} >
                    <div className="w-full max-w-full px-3 flex-0 sm:w-2/12 ">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="quantity">Quantity</label>
                      <input 
                      {...register(`equipment.${index}.quantity`)} placeholder=""
                      id="quantity"
                      type="text" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>

                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-6/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="description">Description</label>
                      <input 
                      {...register(`equipment.${index}.description`)} placeholder=""
                      id="description"
                      type="text" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>

                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-2/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="unit_cost">Unit Cost</label>
                      <input {...register(`equipment.${index}.unit_cost`)} placeholder=""
                      id="unit_cost"
                      className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>

                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-1/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="cost">$</label>
                      <input {...register(`equipment.${index}.cost`)} placeholder=""
                      id="cost"
                       className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>

                    <div className="p-4 rounded  sm:w-1/12">
                      <button type="button" onClick={() => removeEquipment(index)} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding mt-2 mr-2 px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">X</button>
                    </div>
                  
                  </div>

                ))}



                <p className='md:mt-3 leading-normal text-sm '><b>SUPPLIES </b> (list specific items) &nbsp;&nbsp;<button type="button" onClick={() => appendSupplies({ quantity: "", description: "", unit_cost: "", cost: "" })} className="float-right"> + Add Entry
                </button></p>
                

                {suppliesFields.map((field, index) => (
                  <div className="flex flex-wrap mt-2 -mx-3 border-b border-solid border-gray-300  mb-5" key={field.id} >
                    <div className="w-full max-w-full px-3 flex-0 sm:w-2/12 ">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="quantity">Quantity</label>
                      <input 
                      {...register(`supplies.${index}.quantity`)} placeholder=""
                      id="quantity"
                      type="text" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>

                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-6/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="description">Description</label>
                      <input 
                      {...register(`supplies.${index}.description`)} placeholder=""
                      id="description"
                      type="text" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>

                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-2/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="unit_cost">Unit Cost</label>
                      <input {...register(`supplies.${index}.unit_cost`)} placeholder=""
                      id="unit_cost"
                      className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>

                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-1/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="cost">$</label>
                      <input {...register(`supplies.${index}.cost`)} placeholder=""
                      id="cost"
                       className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>

                    <div className="p-4 rounded  sm:w-1/12">
                      <button type="button" onClick={() => removeSupplies(index)} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding mt-2 mr-2 px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">X</button>
                    </div>
                  
                  </div>

                ))}


                <p className='md:mt-3 leading-normal text-sm '><b>OTHER EXPENSES </b> (be specific) &nbsp;&nbsp;<button type="button" onClick={() => appendOtherExpenses({ quantity: "", description: "", unit_cost: "", cost: "" })} className="float-right"> + Add Entry
                </button></p>
                

                {otherexpensesFields.map((field, index) => (
                  <div className="flex flex-wrap mt-2 -mx-3 border-b border-solid border-gray-300 mb-5" key={field.id} >
                    <div className="w-full max-w-full px-3 flex-0 sm:w-2/12 ">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="quantity">Quantity</label>
                      <input 
                      {...register(`otherexpenses.${index}.quantity`)} placeholder=""
                      id="quantity"
                      type="text" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>

                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-6/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="description">Description</label>
                      <input 
                      {...register(`otherexpenses.${index}.description`)} placeholder=""
                      id="description"
                      type="text" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>

                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-2/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="unit_cost">Unit Cost</label>
                      <input {...register(`otherexpenses.${index}.unit_cost`)} placeholder=""
                      id="unit_cost"
                      className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>

                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-1/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="cost">$</label>
                      <input {...register(`otherexpenses.${index}.cost`)} placeholder=""
                      id="cost"
                       className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>

                    <div className="p-4 rounded  sm:w-1/12">
                      <button type="button" onClick={() => removeOtherExpenses(index)} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding mt-2 mr-2 px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">X</button>
                    </div>
                  
                  </div>

                ))}

                 

                  <div className="flex flex-wrap mt-4 -mx-3">
                    <div className="w-full max-w-full px-3 flex-0 sm:w-3/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="totalfundrequested">Total Fund Requested</label>
                      <input 
                      {...register(`totalfundrequested`)} placeholder=""
                      id="totalfundrequested"
                      type="text" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none "/>
                    </div>
                   
                  </div>

                  <div className="flex justify-end mt-6 ">
                    <button type="submit" aria-controls="address" next-form-btn=""  className="inline-block px-6 py-3 m-0 ml-2 text-xs font-bold text-center text-white uppercase align-middle transition-all border-0 rounded-lg cursor-pointer ease-soft-in leading-pro tracking-tight-soft bg-gradient-to-tl from-purple-700 to-pink-500 shadow-soft-md bg-150 bg-x-25 hover:scale-102 active:opacity-85">Save and Continue</button>
                  </div>
            </div>
      </div>

  </form>
  
  </div>


  );
}
        
      
