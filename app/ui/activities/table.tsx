import { fetchFilteredActivities } from '@/app/lib/data';
import { formatDateToLocal } from '@/app/lib/utils';
import { UpdateActivity, DeleteActivity } from './buttons';

export default async function ActivitiesTable({
  query,
  currentPage,
}: {
  query: string;
  currentPage: number;
}) {
  const activities = await fetchFilteredActivities(query, currentPage);

  return (
    <div className="mt-6 flow-root">
      <div className="inline-block min-w-full align-middle">
        <div className="rounded-lg bg-gray-50 p-2 md:pt-0 overflow-x-auto">
          {/* Mobile view */}
          <div className="md:hidden">
            {activities?.map((activity) => (
              <div
                key={activity.id}
                className="mb-2 w-full rounded-md bg-white p-4"
              >
                <div className="flex w-full items-center justify-between pt-4">
                  <div>
                    <p className="break-words">{formatDateToLocal(activity.date)}</p>
                    <p className="text-sm break-words">{activity.venue}</p>
                  </div>
                </div>
                <div className="flex items-center justify-between border-b pb-4">
                  <div>
                    <div className="mb-2">
                      <p className="text-sm text-gray-500 break-words">{activity.attendee}</p>
                      <p className="text-sm text-gray-500 break-words">{activity.publication}</p>
                      <p className="font-medium break-words">{activity.summary}</p>
                    </div>
                  </div>
                  <div className="flex justify-end gap-3">
                    <UpdateActivity id={activity.id} />
                    <DeleteActivity id={activity.id} />
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          {/* Desktop view */}
          <table className="hidden md:table w-full text-gray-900">
            <thead className="rounded-lg text-left text-sm font-normal">
              <tr>
                <th scope="col" className="px-3 py-5 font-medium min-w-[120px]">
                  Date
                </th>
                <th scope="col" className="px-3 py-5 font-medium min-w-[150px]">
                  Venue
                </th>
                <th scope="col" className="px-3 py-5 font-medium min-w-[120px]">
                  Attendee
                </th>
                <th scope="col" className="px-3 py-5 font-medium min-w-[150px]">
                  Publication
                </th>
                <th scope="col" className="px-4 py-5 font-medium sm:pl-6">
                  Summary
                </th>
                <th scope="col" className="px-4 py-5 font-medium min-w-[120px]">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white">
              {activities?.map((activity) => (
                <tr
                  key={activity.id}
                  className="w-full border-b py-3 text-sm last-of-type:border-none [&:first-child>td:first-child]:rounded-tl-lg [&:first-child>td:last-child]:rounded-tr-lg [&:last-child>td:first-child]:rounded-bl-lg [&:last-child>td:last-child]:rounded-br-lg"
                >
                  <td className="px-3 py-3 break-words">
                    {formatDateToLocal(activity.date)}
                  </td>
                  <td className="px-3 py-3 break-words">
                    {activity.venue}
                  </td>
                  <td className="px-3 py-3 break-words">
                    {activity.attendee}
                  </td>
                  <td className="px-3 py-3 break-words">
                    {activity.publication}
                  </td>
                  <td className="px-3 py-3 break-words">
                    <p className="break-words">{activity.summary}</p>
                  </td>
                  <td className="px-3 py-3">
                    <div className="flex justify-end gap-3">
                      <UpdateActivity id={activity.id} />
                      <DeleteActivity id={activity.id} />
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}