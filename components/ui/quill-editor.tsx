'use client';
// components/QuillEditor.tsx
import React from 'react';
import { useId } from 'react';

import dynamic from 'next/dynamic';
import { Controller } from 'react-hook-form';
import 'react-quill-new/dist/quill.snow.css';
import CustomToolbar from '@/components/ui/toolbar';

// Dynamically import ReactQuill to avoid SSR issues
//import ReactQuill from 'react-quill-new';

const ReactQuill = dynamic(() => import('react-quill-new'), {
  ssr: false,
});

interface QuillEditorProps {
  name: string;
  control: any;
  placeholder?: string;
  defaultValue?: string;
  className?: string;
}

const formats = [
  'header', 'bold', 'italic', 'underline', 'link', 'image', 'list', 'bullet', 'indent',
];


const QuillEditor: React.FC<QuillEditorProps> = ({
  name,
  control,
  defaultValue = '',
  placeholder = '',
  className = '',
}) => {

  const toolbarId = useId(); 

  const modules = {
    toolbar: {
      container: `#toolbar-${toolbarId}`,
    },
  };

  return (
    <div className="form-group">
      
      <CustomToolbar id={toolbarId}  />
      {/* ReactQuill Editor */}
      <Controller
        name={name}
        control={control}
        defaultValue={defaultValue}
        render={({ field }) => (
          <ReactQuill
            modules={modules}
            formats={formats}
            theme="snow"
            value={field.value}
            onChange={field.onChange}
            placeholder={placeholder}
            className={`quill-editor ${className}`}
          />
        )}
      />
    </div>
  );
};

export default QuillEditor;